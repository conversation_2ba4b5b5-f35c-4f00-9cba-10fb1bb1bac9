# /// script
# requires-python = ">=3.12"
# dependencies = [
#     "wikipedia-api",
#     "fastmcp",
#     "duckduckgo-search",
#     "requests",
#     "beautifulsoup4",
# ]
# ///
#
# uv run script.py installe automatiquement les dépendances
#

import asyncio
import urllib.parse
from datetime import datetime

# https://github.com/encode/httpx
import httpx

# https://github.com/martin-maj<PERSON>/Wikipedia-API
import wikipediaapi

# https://github.com/jlowin/fastmcp
# https://gofastmcp.com/
from fastmcp import FastMCP

# https://github.com/deedy5/duckduckgo_search
from duckduckgo_search import DDGS

# https://github.com/psf/requests
import requests

# https://www.crummy.com/software/BeautifulSoup/
from bs4 import BeautifulSoup

wiki_api = wikipediaapi.Wikipedia(
    user_agent="FastMCPServer/1.0 (https://gofastmcp.com/)",
    language="fr",
    extract_format=wikipediaapi.ExtractFormat.WIKI,
)

mcp = FastMCP(
    "Wikipedia API Server",
    instructions=(
        "Provides tools to interact with Wikipedia. When specifying an article, "
        "ensure the title is formatted correctly for a URL, replacing spaces with "
        "underscores and using parentheses for disambiguation, e.g., 'Python_(programming_language)'."
    ),
)


def search_duckduckgo_lite(query_term):
    """
    Effectue une recherche sur DuckDuckGo Lite et retourne les résultats formatés.

    Cette fonction effectue une recherche web en utilisant l'interface légère de DuckDuckGo,
    analyse la réponse HTML et retourne les résultats de recherche formatés incluant
    les titres, URLs et extraits.

    Args:
        query_term (str): La requête de recherche à exécuter sur DuckDuckGo Lite.

    Returns:
        str: Une chaîne formatée contenant les résultats de recherche avec :
            - Le nombre de résultats trouvés
            - Pour chaque résultat : titre, URL et extrait
            - Résultats séparés par des tirets

            En cas d'erreur, retourne un message d'erreur en français décrivant le problème.

    Raises:
        La fonction gère toutes les exceptions en interne et retourne des messages d'erreur
        en français au lieu de lever des exceptions :
        - Erreurs réseau (requests.exceptions.RequestException)
        - Erreurs d'analyse HTML
        - Erreurs de code de statut HTTP
        - Erreurs inattendues

    Example:
        >>> results = search_duckduckgo_lite("programmation python")
        >>> print(results)
        Trouvé 10 résultats:
        1. Tutoriel de Programmation Python
           URL: https://example.com/python
           Snippet: Apprenez les bases de la programmation Python...
        ---
        ...

    Note:
        - Utilise l'interface HTML simple de DuckDuckGo Lite
        - Analyse les résultats par groupes de 4 lignes de tableau (titre/lien, extrait, URL, espacement)
        - Les messages d'erreur sont affichés en français
        - Retourne des résultats vides si aucun résultat de recherche valide n'est trouvé
    """
    url = "https://lite.duckduckgo.com/lite/"
    payload = {"q": query_term}

    try:
        response = requests.post(url, data=payload)

        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")
            results = []

            # Results are grouped in sets of 4 <tr> tags: title/link, snippet, URL, spacer
            rows = soup.find_all("tr")
            i = 0
            while i < len(rows):
                try:
                    link_tag = rows[i].find("a", class_="result-link")
                    if not link_tag:
                        i += 1
                        continue

                    title = link_tag.get_text(strip=True)
                    url = link_tag.get("href", "")

                    # Second row: snippet
                    snippet_row = rows[i + 1]
                    snippet = snippet_row.get_text(strip=True) if snippet_row else ""

                    # Third row: the visible plain URL (optional)
                    visible_url_row = rows[i + 2]
                    visible_url = (
                        visible_url_row.get_text(strip=True) if visible_url_row else ""
                    )

                    results.append(
                        {
                            "title": title,
                            "url": url,
                            "snippet": snippet,
                            "visible_url": visible_url,
                        }
                    )

                    i += 4
                except Exception as parse_error:
                    return f"Erreur d'analyse à la ligne {i}: {parse_error}"

            output = f"Trouvé {len(results)} résultats:\n"
            for idx, result in enumerate(results, start=1):
                output = output + f"{idx}. {result['title']}" + "\n"
                output = output + f"   URL: {result['url']}" + "\n"
                output = output + f"   Snippet: {result['snippet']}" + "\n"
                output = output + "-" * 3 + "\n"
            return output
        else:
            return f"Erreur: HTTP {response.status_code}" + "/n" + response.text
    except requests.exceptions.RequestException as e:
        return f"Erreur réseau: {e}"
    except Exception as e:
        return f"Erreur inattendue: {e}"


def search_duckduckgo_api(query_term):
    """
    Effectue une recherche web en utilisant l'API DuckDuckGo.

    Args:
        query_term (str): Le terme de recherche à utiliser pour la requête.

    Returns:
        list: Une liste de résultats de recherche contenant des dictionnaires avec
              les clés 'title', 'href', 'body' pour chaque résultat trouvé.
              Maximum 10 résultats retournés.

    Note:
        La recherche est configurée pour la région française ("fr-fr") et
        retourne un maximum de 10 résultats.
    """
    return DDGS().text(
        keywords=query_term,
        region="fr-fr",
        #backend="html",
        max_results=10,
    )


async def search_wikipedia(search_term: str, max_results: int = 10) -> str:
    """
    Searches a term directly on Wikipedia using the MediaWiki API and returns formatted results.

    Args:
        search_term: The term to search for on Wikipedia.
        max_results: The maximum number of results to return.

    Returns:
        Une chaîne formatée contenant les résultats de recherche ou un message d'erreur.
    """
    BASE_URL = "https://fr.wikipedia.org/w/api.php"
    params = {
        "action": "query",
        "format": "json",
        "list": "search",
        "srsearch": search_term,
        "srlimit": max_results,
        "srprop": "snippet|titlesnippet",
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(BASE_URL, params=params, timeout=15.0)
            response.raise_for_status()
            data = response.json()

        results = []
        for item in data.get("query", {}).get("search", []):
            title = item.get("title", "")
            snippet = item.get("snippet", "")

            snippet = snippet.replace('<span class="searchmatch">', "").replace(
                "</span>", ""
            )
            title_for_url = title.replace(" ", "_")
            title_encoded = urllib.parse.quote(title_for_url, safe="()")

            results.append(
                {
                    "title": title,
                    "link": f"https://fr.wikipedia.org/wiki/{title_encoded}",
                    "snippet": snippet,
                }
            )

        if not results:
            return "Aucun résultat trouvé pour votre recherche sur Wikipedia."

        output_lines = [f"Trouvé {len(results)} résultats Wikipedia:\n"]
        for i, result in enumerate(results, 1):
            output_lines.append(f"{i}. {result['title']}")
            output_lines.append(f"   URL: {result['link']}")
            output_lines.append(f"   Résumé: {result['snippet']}\n")

        return "\n".join(output_lines)
    except httpx.HTTPError as e:
        return f"Erreur HTTP survenue : {e}"
    except Exception as e:
        return f"Erreur inattendue : {e}"


@mcp.tool
def search_duckduckgo_for_article(search_text: str) -> str:
    """
    Searches on DuckDuckGo

    Gives you a list of HTML pages that match search query.
    """

    #return search_duckduckgo_lite(search_text)  # type: ignore
    return search_duckduckgo_api(search_text)  # type: ignore


@mcp.tool
def search_wikipedia_for_article(search_text: str) -> str:
    """
    Searches on Wikipedia

    Gives you a list of Wikipedia articles that match your search query.
    """

    return search_wikipedia(search_text, max_results=10)  # type: ignore


@mcp.tool
def get_wikipedia_article_summary(article_title: str) -> str:
    """
    Retrieves the summary of a specific Wikipedia article.

    The article_title must be formatted for a URL, with spaces replaced
    by underscores and disambiguation in parentheses, e.g., 'Python_(programming_language)'.
    """
    article = wiki_api.page(article_title)
    if article.exists():
        return article.summary
    else:
        return f"Erreur : L'article '{article_title}' n'existe pas sur Wikipédia."


@mcp.tool
def get_wikipedia_full_article(article_title: str) -> str:
    """
    Retrieves the full text content of a specific Wikipedia article.

    The article_title must be formatted for a URL, with spaces replaced
    by underscores and disambiguation in parentheses, e.g., 'Python_(programming_language)'.
    """
    article = wiki_api.page(article_title)
    if article.exists():
        return article.text
    else:
        return f"Erreur : L'article '{article_title}' n'existe pas sur Wikipédia."


@mcp.tool()
def get_time() -> str:
    return datetime.now().isoformat()


@mcp.resource("config://version")
def get_version():
    return "1.0.1"


if __name__ == "__main__":
    mcp.run(transport="http", host="127.0.0.1", port=8000)
