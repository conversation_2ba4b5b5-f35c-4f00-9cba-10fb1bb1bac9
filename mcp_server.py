import socket
import async<PERSON>
import j<PERSON>
from datetime import datetime
from typing import List, Dict, Any

# https://github.com/jlowin/fastmcp
from mcp.server.fastmcp import FastMCP, Context
from mcp.server.fastmcp.utilities.logging import get_logger

# https://github.com/fixie-ai/lmstudio
import lmstudio as lms

# https://github.com/deedy5/duckduckgo_search
from ddgs import DDGS
from ddgs.exceptions import DDGSException, RatelimitException, TimeoutException

# https://github.com/csparpa/pyowm
from pyowm import OWM
from pyowm.commons.exceptions import (
    APIRequestError,
    APIResponseError,
    NotFoundError,
)

logger = get_logger(__name__)


def get_lan_ip() -> str:
    """Get the LAN IP of the machine (e.g., 192.168.x.x)."""
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
    except OSError:
        ip = "127.0.0.1"
    finally:
        s.close()
    return ip


mcp = FastMCP(
    name="lmstudio_mcp_server",
    instructions="""
    MCP Server with web search and international weather tools.

    TOOL USAGE GUIDELINES:

    1. current_weather: WORLDWIDE weather data
       - Works for any city globally
       - Supports country format as ISO 3166-1 alpha-2 codes
       - Examples: "Paris, FR", "London, UK", "Tokyo, JP"
       - Returns temperature in both Celsius and Fahrenheit

    2. search_web: Web search via DuckDuckGo
       - French region by default
       - Max results: 1-20 (default: 10)

    3. get_current_model: LM Studio model information

    Handle errors gracefully and provide helpful suggestions to users.
    """,
)


@mcp.resource("resource://sys/info")
def sys_info() -> str:
    """Server system information and capabilities"""
    data = {
        "server_info": {
            "name": "TestSearch MCP Server",
            "version": "2.0.0",
            "lan_ip": get_lan_ip(),
            "hostname": socket.gethostname(),
            "timestamp": datetime.now().isoformat(),
            "status": "active",
        },
        "capabilities": {
            "weather": {
                "scope": "International",
                "coverage": "Worldwide cities",
                "provider": "OpenWeatherMap",
                "features": [
                    "Multi-unit temperature (°C/°F)",
                    "Multi-unit wind speed (km/h, mph, m/s)",
                    "Multi-unit pressure (mbar, hPa, inHg)",
                    "Country disambiguation",
                    "Comprehensive atmospheric data",
                ],
            },
            "search": {
                "scope": "Global web search",
                "provider": "DuckDuckGo",
                "features": [
                    "Multiple search backends",
                    "Rate limit handling",
                    "French region preference",
                    "Configurable result count",
                ],
            },
            "lm_studio": {
                "scope": "Local model information",
                "features": [
                    "Current model status",
                    "Model metadata",
                    "Connection status",
                ],
            },
        },
        "supported_countries": {
            "note": "Weather tool supports all countries in OpenWeatherMap database",
            "examples": {
                "Americas": ["US", "CA", "BR", "MX", "AR"],
                "Europe": ["FR", "GB", "DE", "IT", "ES", "NL", "CH"],
                "Asia": ["JP", "CN", "IN", "KR", "TH", "SG"],
                "Oceania": ["AU", "NZ"],
                "Africa": ["ZA", "EG", "KE", "NG"],
                "Middle East": ["AE", "SA", "IL", "TR"],
            },
        },
        "usage_stats": {
            "total_tools": 3,
            "total_resources": 4,
            "international_support": True,
            "last_updated": datetime.now().isoformat(),
        },
    }
    return json.dumps(data, indent=2, ensure_ascii=False)


@mcp.resource("resource://tools/usage")
def tools_usage_guide() -> str:
    """Comprehensive guide for tool usage and capabilities."""
    return json.dumps(
        {
            "weather_tool": {
                "name": "current_weather",
                "scope": "International - any city worldwide",
                "parameters": {
                    "city_name": {
                        "required": True,
                        "type": "string",
                        "description": "Name of the city",
                    },
                    "country": {
                        "required": True,
                        "type": "string",
                        "description": "2-letter ISO 3166-1 alpha-2 code",
                    },
                },
                "valid_examples": [
                    "current_weather('Paris', 'US')",
                    "current_weather('London', 'UK')",
                    "current_weather('Tokyo', 'JP')",
                    "current_weather('Berlin', 'DE')",
                ],
                "invalid_examples": [
                    "current_weather('Paris')",  # Missing country code
                    "current_weather('Paris', 'Texas')",  # Invalid country code
                ],
                "features": [
                    "Temperature in Celsius and Fahrenheit",
                    "Wind speed in km/h, mph, and m/s",
                    "Pressure in mbar, hPa, and inHg",
                    "Humidity, cloud coverage, visibility",
                    "Precipitation data (rain/snow)",
                ],
                "disambiguation": {
                    "note": "Many cities share names across countries",
                    "examples": {
                        "Paris": "Paris, France (default) vs Paris, Texas (with country='US')",
                        "London": "London, UK (default) vs London, Ontario (with country='CA')",
                        "Springfield": "Multiple cities in US - specify state if needed",
                    },
                },
            },
            "search_tool": {
                "name": "search_web",
                "scope": "Global web search via DuckDuckGo",
                "parameters": {
                    "query": {
                        "required": True,
                        "type": "string",
                        "description": "Search query",
                    },
                    "max_results": {
                        "required": False,
                        "type": "integer",
                        "default": 10,
                        "range": "1-20",
                        "description": "Number of results to return",
                    },
                },
                "features": [
                    "French region preference by default",
                    "Multiple search backends",
                    "Rate limit handling",
                    "Timeout management",
                ],
            },
            "model_tool": {
                "name": "get_current_model",
                "scope": "LM Studio model information",
                "parameters": "None required",
                "returns": "Current model details and status",
            },
        },
        indent=2,
        ensure_ascii=False,
    )


@mcp.resource("resource://tools/documentation")
def tools_documentation() -> str:
    """Complete documentation for all available tools and their usage rules."""
    documentation = {
        "server_info": {
            "name": "TestSearch MCP Server",
            "version": "2.0",
            "capabilities": [
                "International weather data",
                "Web search",
                "LM Studio integration",
            ],
        },
        "tools_overview": {
            "current_weather": {
                "purpose": "Get weather information for any city worldwide",
                "international_support": True,
                "disambiguation": "Use country parameter for cities with common names",
                "data_units": {
                    "temperature": ["Celsius", "Fahrenheit"],
                    "wind_speed": ["km/h", "mph", "m/s"],
                    "pressure": ["mbar", "hPa", "inHg"],
                },
                "usage_patterns": [
                    {
                        "pattern": "current_weather('CityName', 'CountryCode')",
                        "example": "current_weather('Paris', 'FR') → Paris, France",
                    },
                    {
                        "pattern": "current_weather('CityName', 'CountryCode')",
                        "example": "current_weather('London', 'CA') → London, Ontario",
                    },
                ],
            },
            "search_web": {
                "purpose": "Perform web searches using DuckDuckGo",
                "features": [
                    "Multiple backends",
                    "Rate limit handling",
                    "French region preference",
                ],
                "usage_patterns": [
                    "search_web('query') → 10 results",
                    "search_web('query', max_results=5) → 5 results",
                ],
            },
            "get_current_model": {
                "purpose": "Get LM Studio model information",
                "parameters": "None",
                "returns": "Model details in JSON format",
            },
        },
        "error_handling": {
            "weather_tool": [
                "Empty city name → Usage instructions with examples",
                "City not found → Suggestions and troubleshooting",
                "API error → Error details with retry suggestions",
            ],
            "search_tool": [
                "Rate limit → Wait time suggestions",
                "Timeout → Retry recommendations",
                "Service error → Alternative query suggestions",
            ],
        },
        "best_practices": [
            "Always specify country code",
            "Use major cities when possible for better data availability",
            "Check error messages for specific usage guidance",
            "Weather data includes multiple unit systems for international use",
        ],
    }

    return json.dumps(documentation, indent=2, ensure_ascii=False)


@mcp.tool()
async def get_current_model() -> str:
    """Get the currently loaded model in LM Studio.

    Returns:
        JSON with model information or error details
    """
    try:
        model = lms.llm()
        return json.dumps(model.get_info().to_dict(), indent=2)
    except AttributeError as e:
        return json.dumps({"error": f"AttributeError: {str(e)}"})
    except lms.LMStudioError as e:
        return json.dumps({"error": f"LMStudioError: {str(e)}"})


@mcp.tool()
def current_weather(city_name: str, country_code: str = "") -> str:
    """Get current weather information for any city worldwide.

    Args:
        city_name: Name of the city (required)
        country_code: 2-letter ISO 3166-1 alpha-2 code (required)
                Examples: 'FR','US', 'UK', 'GB'

    Returns:
        JSON with detailed weather information including both Celsius/Fahrenheit

    Examples:
        ✅ current_weather("Paris")
        ✅ current_weather("Paris", "France")
        ✅ current_weather("Paris", "FR")
        ✅ current_weather("Paris", "US")  # Paris, Texas
        ✅ current_weather("London", "UK")
        ✅ current_weather("Tokyo", "Japan")
        ✅ current_weather("New York")
    """

    if not city_name or city_name.strip() == "":
        return json.dumps(
            {
                "error": "City name is required and cannot be empty",
                "usage_examples": [
                    "current_weather('Paris')",
                    "current_weather('London', 'UK')",
                    "current_weather('Tokyo', 'Japan')",
                    "current_weather('New York', 'US')",
                    "current_weather('Berlin', 'Germany')",
                ],
                "supported_countries": "Any country - use full names or ISO codes",
            },
            indent=2,
            ensure_ascii=False,
        )

    city_name = city_name.strip()

    if not country_code and country_code.strip() == "":
        return json.dumps(
            {
                "error": "Country code is required and cannot be empty",
                "usage_examples": [
                    "current_weather('Paris', 'FR')",
                    "current_weather('London', 'UK')",
                    "current_weather('Tokyo', 'JP')",
                    "current_weather('New York', 'US')",
                    "current_weather('Berlin', 'DE')",
                ],
                "supported_countries": "Use ISO 3166-1 alpha-2 codes",
            },
            indent=2,
            ensure_ascii=False,
        )

    location_query = f"{city_name},{country_code}"

    try:
        # OpenWeatherMap API Key:
        #   Make an account here : https://home.openweathermap.org/
        #   Check your API key here : https://home.openweathermap.org/api_keys
        owm = OWM("********************************")
        mgr = owm.weather_manager()

        # https://api.openweathermap.org/data/2.5/weather?q=" + city + "," + country + "&appid=" + api_key + "&units=" + id(weather_units) + "&lang=" + id(weather_language));
        # https://api.openweathermap.org/data/2.5/weather?q=evreux,france&appid=********************************&units=metric&lang=fr
        # https://openweathermap.org/current#fields_json
        weather_observation = mgr.weather_at_place(location_query)

        if weather_observation is None or weather_observation.weather is None:
            return json.dumps(
                {
                    "error": f"No weather data found for '{city_name}'",
                    "searched_for": location_query,
                    "suggestions": {
                        "spelling": "Check the city name spelling",
                        "major_cities": "Try major cities first",
                        "country_help": "Add a country for better accuracy",
                        "examples": [
                            "Paris (finds Paris, France automatically)",
                            "Paris, US (finds Paris, Texas)",
                            "Springfield, US (there are many Springfields!)",
                        ],
                    },
                },
                indent=2,
                ensure_ascii=False,
            )

        wind = weather_observation.weather.wind()
        temperature = weather_observation.weather.temperature("celsius")
        pressure = weather_observation.weather.pressure

        weather_data = {
            "location": {
                "city": weather_observation.location.name,
                "country": weather_observation.location.country,
                "coordinates": {
                    "latitude": weather_observation.location.lat,
                    "longitude": weather_observation.location.lon,
                },
            },
            "current_weather": {
                "condition": weather_observation.weather.detailed_status,
                "icon_code": weather_observation.weather.weather_icon_name,
                "temperature": {
                    "celsius": temperature["temp"],
                    "fahrenheit": round(temperature["temp"] * 9 / 5 + 32, 1),
                    "feels_like": {
                        "celsius": temperature["feels_like"],
                        "fahrenheit": round(temperature["feels_like"] * 9 / 5 + 32, 1),
                    },
                },
                "wind": {
                    "speed": {
                        "kmh": round(wind["speed"] * 3.6, 2),
                        "mph": round(wind["speed"] * 2.237, 2),
                        "ms": wind["speed"],
                    },
                    "direction_degrees": wind.get("deg", 0),
                },
                "atmosphere": {
                    "humidity_percent": weather_observation.weather.humidity,
                    "pressure": {
                        "mbar": pressure.get("press", 0),
                        "hPa": pressure.get("press", 0),
                        "inHg": round(pressure.get("press", 0) * 0.02953, 2),
                    },
                    "cloud_coverage_percent": weather_observation.weather.clouds,
                },
                "visibility_km": round(
                    weather_observation.weather.visibility_distance / 1000, 2
                )
                if weather_observation.weather.visibility_distance
                else None,
                "precipitation": {
                    "rain": weather_observation.weather.rain
                    if weather_observation.weather.rain
                    else None,
                    "snow": weather_observation.weather.snow
                    if weather_observation.weather.snow
                    else None,
                },
            },
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "query_used": location_query,
                "data_source": "OpenWeatherMap",
            },
        }

        return json.dumps(weather_data, indent=2, ensure_ascii=False)
    except AttributeError as e:
        return json.dumps(
            {
                "error": "Attribute error in weather data",
                "details": str(e),
            },
            indent=2,
            ensure_ascii=False,
        )
    except KeyError as e:
        return json.dumps(
            {
                "error": "Key error in weather data",
                "details": str(e),
            },
            indent=2,
            ensure_ascii=False,
        )
    except TypeError as e:
        return json.dumps(
            {
                "error": "Type error in weather data",
                "details": str(e),
            },
            indent=2,
            ensure_ascii=False,
        )
    except NotFoundError as e:
        return json.dumps(
            {
                "error": "City not found in OpenWeatherMap database",
                "details": str(e),
                "city_requested": city_name,
                "country_requested": country_code,
                "query_attempted": location_query,
            },
            indent=2,
            ensure_ascii=False,
        )
    except APIRequestError as e:
        return json.dumps(
            {
                "error": "API request error from OpenWeatherMap",
                "details": str(e),
                "city_requested": city_name,
                "country_requested": country_code,
                "query_attempted": location_query,
            },
            indent=2,
            ensure_ascii=False,
        )
    except APIResponseError as e:
        return json.dumps(
            {
                "error": "API response error from OpenWeatherMap",
                "details": str(e),
                "city_requested": city_name,
                "country_requested": country_code,
                "query_attempted": location_query,
            },
            indent=2,
            ensure_ascii=False,
        )


@mcp.tool()
def search_web(query: str, max_results: int = 10) -> List[Dict[str, Any]]:
    """Web search using DuckDuckGo.

    Args:
        query: Search query (required)
        max_results: Number of results to return (1-20, default: 10)

    Returns:
        List of search results with title, URL, snippet, and date
    """

    """
    TODO
    https://python.langchain.com/api_reference/community/utilities/langchain_community.utilities.duckduckgo_search.DuckDuckGoSearchAPIWrapper.html

        https://github.com/deedy5/ddgs/blob/main/ddgs/engines/duckduckgo.py#L30
        `payload = {"q": query, "b": "", "l": region}` pas l mais kl
        => https://html.duckduckgo.com/html/?q=open%20source%20llm%202025&kl=fr-fr
        comme ici https://github.com/nickclyde/duckduckgo-mcp-server/blob/main/src/duckduckgo_mcp_server/server.py#L80

        https://github.com/deedy5/ddgs/blob/main/ddgs/base.py#L101
    """

    try:
        max_results = max(1, min(max_results, 20))
        logger.info("Web search - query: '%s' (max_results=%d)", query, max_results)

        with DDGS() as ddgs:
            results = list(
                ddgs.text(
                    query=query,
                    max_results=max_results,
                    region="fr-fr",
                    safesearch="off",
                    backend="google,mullvad_google,mojeek",
                )
            )

        formatted_results = []
        for result in results:
            formatted_results.append(
                {
                    "title": result.get("title", ""),
                    "url": result.get("href", ""),
                    "snippet": result.get("body", ""),
                    "date": result.get("date", ""),
                }
            )
        return formatted_results
    except RatelimitException as e:
        return [
            {
                "error": "Rate limit exceeded",
                "details": str(e),
                "suggestion": "Try again in a few minutes",
            }
        ]
    except TimeoutException as e:
        return [
            {
                "error": "Search timeout",
                "details": str(e),
                "suggestion": "Try again or use a different query",
            }
        ]
    except DDGSException as e:
        return [{"error": "Search service error", "details": str(e)}]



@mcp.tool()
async def does_sampling_work(ctx: Context):
    """
    Performs a sampling query with a given context and customized system prompt.

    Returns:
        Any: The result of the sampling operation performed by the context.
    """
    # The 'sample' method does not exist on Context; replace with a valid method or remove.
    # For demonstration, simply return a static message or use another valid Context method if available.
    return "Does the MCP sampling work? (sampling method not available in Context)"

if __name__ == "__main__":
    asyncio.run(mcp.run_stdio_async())
